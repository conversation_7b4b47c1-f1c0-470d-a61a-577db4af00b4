import React, { Component } from 'react';
import { View, StyleSheet } from 'react-native';
import SegmentWithLoadingControl from './com.xiaomi.cariot.gesture/Main/car/widget/SegmentWithLoadingControl';

/**
 * 测试SegmentWithLoadingControl组件的居中对齐和loading功能
 */
class SegmentWithLoadingControlTest extends Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedIndex: 0,
      disabled: false
    };
  }

  handleSegmentChange = (index) => {
    console.log('分段切换，新索引:', index);
    this.setState({ selectedIndex: index });
  };

  render() {
    return (
      <View style={styles.container}>
        {/* 测试短文本的居中对齐 */}
        <SegmentWithLoadingControl
          title="短文本测试"
          values={['A', 'B', 'C']}
          tipsArray={['选项A', '选项B', '选项C']}
          selectedIndex={this.state.selectedIndex}
          onChange={this.handleSegmentChange}
          disabled={this.state.disabled}
          style={styles.segmentStyle}
        />

        {/* 测试长文本的居中对齐 */}
        <SegmentWithLoadingControl
          title="长文本测试"
          values={['选项一', '选项二', '选项三']}
          tipsArray={['这是选项一', '这是选项二', '这是选项三']}
          selectedIndex={this.state.selectedIndex}
          onChange={this.handleSegmentChange}
          disabled={this.state.disabled}
          style={styles.segmentStyle}
        />

        {/* 测试不同长度文本的居中对齐 */}
        <SegmentWithLoadingControl
          title="混合长度测试"
          values={['短', '中等长度', '非常长的选项文本']}
          tipsArray={['短文本', '中等长度文本', '非常长的选项文本说明']}
          selectedIndex={this.state.selectedIndex}
          onChange={this.handleSegmentChange}
          disabled={this.state.disabled}
          style={styles.segmentStyle}
        />
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5'
  },
  segmentStyle: {
    marginBottom: 30
  }
});

export default SegmentWithLoadingControlTest;

/**
 * 测试要点：
 * 
 * 1. 居中对齐测试：
 *    - 点击任意选项，观察loading图标和文字是否作为整体居中
 *    - 测试不同长度的文本，确保居中效果一致
 * 
 * 2. Loading功能测试：
 *    - 点击选项后立即切换并显示loading
 *    - Loading图标在文字左侧，间隔8像素
 *    - Loading图标大小40x40，带旋转动画
 *    - 3秒后自动隐藏loading
 * 
 * 3. 交互测试：
 *    - Loading期间点击其他选项应被忽略
 *    - onChange回调立即触发
 * 
 * 4. 预期的居中效果：
 *    - 无loading时：文字在容器中央
 *    - 有loading时：[图标 + 8px间隔 + 文字] 整体在容器中央
 */
