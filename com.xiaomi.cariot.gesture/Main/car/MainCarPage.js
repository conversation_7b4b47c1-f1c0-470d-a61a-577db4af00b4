import React from 'react';
import {
  FlatList,
  Image,
  ScrollView,
  Text,
  View
} from 'react-native';

import {
  styles,
  HeaderComponent,
  WarningCard
} from 'micariot-ui-sdk';
import { CarPluginWindow, carStyles } from "./common/Styles";
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { DarkMode, Device, DeviceEvent, Host, Package, PackageEvent, Service } from "miot";

import ImageSegmentControl from "./widget/ImageSegmentControl";
import ClickableCardNew from "./widget/ClickableCardNew";
import ScaleableOpacity from "./widget/ScaleableOpacity";
import native from "miot/native";
import { LEVEL } from "micariot-ui-sdk/components/WarningCard";
import * as CommonUtil from "../util/CommonUtil";
import LogUtil from "../util/LogUtil";
import Toast from "../Toast";
import SegmentWithLoadingControl from "./widget/SegmentWithLoadingControl";

const TAG = "MainCarPage";

const mainStyles = {
  gridContainer: {
    marginTop: 72
  },
  gridItem: {
    height: 132,
    justifyContent: 'center',
    padding: 6
  },
  gridItemContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: DarkMode.getColorScheme() === 'dark' ? "xm#C1CCE22B" : "xm#F0F5FFCC",
    borderRadius: 16,
    height: 120,
    width: '100%',
    paddingLeft: 48,
    paddingRight: 48,
    margin: 0
  },
  gridIcon: {
    width: 40,
    height: 40,
    marginRight: 24
  }
};
const KEY = "GESTURE_INDEX";
const CAR_SUPPORT_SWITCH_VERSION = "3.0.3_0177";
const CAR_SUPPORT_SENSITIVITY_VERSION = "3.0.3_0199";

export default class MainCarPage extends React.Component {
  state = {
    gestureSwitch: false,
    isError: false,
    exist_hypermind: false,
    errorMessage: LocalizedStrings['car_high_temperature_protection'],
    car_did: "",
    gestureIndex: 0,
    selectedGridItem: -1,
    currentSensitivityIndex: 0,
    // 当前手势的绑定类型 0:未设置 1: 预置 2:智能场景 3:自定义
    bindType: -1,
    // 手势的iid 前缀
    gesture_iid: "99.1.",
    gesture_name: [
      LocalizedStrings['car_gesture_yeah'],
      LocalizedStrings['car_gesture_five'],
      LocalizedStrings['car_gesture_thumbleft'],
      LocalizedStrings['car_gesture_thumbright'],
      LocalizedStrings['car_gesture_thumbup'],
      LocalizedStrings['car_gesture_ok']
    ],
    preset_ruleid: [
      "0",
      "1001",
      "1002",
      "1003",
      "1004",
      "1005"
    ],
    tabs: [
      { icon: require('../../Resources/Main/tab1.png'), title: " " },
      { icon: require('../../Resources/Main/tab2.png'), title: " " },
      { icon: require('../../Resources/Main/tab3.png'), title: " " },
      { icon: require('../../Resources/Main/tab4.png'), title: " " },
      { icon: require('../../Resources/Main/tab5.png'), title: " " },
      { icon: require('../../Resources/Main/tab6.png'), title: " " }
    ],
    gridItems: [
      {
        icon: DarkMode.getColorScheme() === 'dark' ? require('../../Resources/Main/grid1_dark.png') : require('../../Resources/Main/grid1.png'),
        text: LocalizedStrings['car_grid_no_function'],
        visible: true
      },
      {
        icon: DarkMode.getColorScheme() === 'dark' ? require('../../Resources/Main/grid2_dark.png') : require('../../Resources/Main/grid2.png'),
        text: LocalizedStrings['car_grid_play_pause'],
        visible: true
      },
      {
        icon: DarkMode.getColorScheme() === 'dark' ? require('../../Resources/Main/grid3_dark.png') : require('../../Resources/Main/grid3.png'),
        text: LocalizedStrings['car_grid_prev'],
        visible: true
      },
      {
        icon: DarkMode.getColorScheme() === 'dark' ? require('../../Resources/Main/grid4_dark.png') : require('../../Resources/Main/grid4.png'),
        text: LocalizedStrings['car_grid_next'],
        visible: true
      },
      {
        icon: DarkMode.getColorScheme() === 'dark' ? require('../../Resources/Main/grid5_dark.png') : require('../../Resources/Main/grid5.png'),
        text: LocalizedStrings['car_grid_map'],
        visible: true
      },
      {
        icon: DarkMode.getColorScheme() === 'dark' ? require('../../Resources/Main/grid6_dark.png') : require('../../Resources/Main/grid6.png'),
        text: LocalizedStrings['car_grid_photo'],
        visible: false
      }
    ],
    customData: {
      // TODO 获取svg图标字符串 传参svgXmlData
      icon: require('../../Resources/Main/card2_icon.png'),
      title: LocalizedStrings['car_custom'],
      tipText: LocalizedStrings['car_not_set_customData']
    },
    autoData: {
      icon: require('../../Resources/Main/card2_icon.png'),
      title: LocalizedStrings['car_automation'],
      tipText: LocalizedStrings['car_not_set_autoData']
    },
    currentAutoRuleId: ""
  };

  constructor(props) {
    super(props);

  }


  componentDidMount() {
    this.getDeviceGesTureBindData(true);
    // 获取温度状态和开关
    this.getThermalStatus();

    this.setAppListener();
  }

  getThermalStatus() {
    this.getGestureSwitch();
    this.getSensitivitySetting();
    Service.spec.getPropertiesValue([{ did: Device.deviceID, siid: 3, piid: 1 }], 2).then((res) => {
      let isOk = res[0].code === 0;
      if (isOk) {
        LogUtil.logOnAll("=============get thermal status success", res);
        this.setState({
          isError: res[0].value === 1
        });
      } else {
        LogUtil.logOnAll("=============get thermal status fail", res);
      }
    }).catch((err) => {
      LogUtil.logOnAll("get thermal status error:", err);
    });
  }

  componentWillUnmount() {
    // 移除所有监听器
    this.viewDidAppearListener && this.viewDidAppearListener.remove();
    this._deviceOnlineListener && this._deviceOnlineListener.remove();
    // 取消订阅
    this.mSubcription && this.mSubcription.remove();
    // 取消监听
    this.mDeviceReceivedMessages && this.mDeviceReceivedMessages.remove();
  }


  render() {
    const errorOpacity = this.state.isError ? 0.35 : 1; // 定义统一的透明度值
    return (
      <View style={carStyles.mediumLargeContainerStyle2}>
        <HeaderComponent
          type={"medium"}
          title={ Device.name ? (Device.name.length > 15 ? `${ Device.name.substr(0, 15) }...` : Device.name) : "" }
          hideSwitch={Device.lastVersion < CAR_SUPPORT_SWITCH_VERSION}
          style={{
            marginLeft: CarPluginWindow.MediumLarge.MarginLeft_Right,
            marginRight: CarPluginWindow.MediumLarge.MarginLeft_Right
          }}
          onClosePress={() => {
            LogUtil.logOnAll("HeaderComponent onClosePress");
            Package.exit();
          }}
          disableSwitch={this.state.isError}
          onSwitchChange={() => {
            LogUtil.logOnAll("onSwitchChange");
            this.setGestureSwitch(!this.state.gestureSwitch)
          }}
          switchValue={this.state.gestureSwitch}
        />
        {this.state.isError && <WarningCard
          level={LEVEL.Error}
          info={this.state.errorMessage}
          style={
            { marginHorizontal: 72 }
          }
        />}
        <ScrollView
          style={[{ flex: 1 }]}
          contentContainerStyle={{
            paddingBottom: CarPluginWindow.MediumLarge.MarginLeft_Right,
            paddingLeft: CarPluginWindow.MediumLarge.MarginLeft_Right,
            paddingRight: CarPluginWindow.MediumLarge.MarginLeft_Right
          }}
          showsVerticalScrollIndicator={true}
          pointerEvents={this.state.isError ? 'none' : 'auto'}
          overScrollMode="never"
        >
          <ImageSegmentControl
            values={this.state.tabs}
            selectedIndex={this.state.gestureIndex}
            onChange={(index) => {
              if (this.state.isError) return;
              if (this.state.gestureIndex === index) {
                return;
              }
              LogUtil.logOnAll(`SegmentControl selected: ${ index }`);
              this.setState({ gestureIndex: index }, () => {
                this.getDeviceGesTureBindData();
              });
            }}
            disabled={this.state.isError}
          />

          <FlatList
            data={this.state.gridItems.filter((item) => item.visible)}
            numColumns={3}
            renderItem={this.renderGridItem}
            keyExtractor={(item, index) => index.toString()}
            contentContainerStyle={[
              mainStyles.gridContainer,
              { opacity: errorOpacity }
            ]}
            scrollEnabled={false}
            ItemSeparatorComponent={() => <View style={{ width: 8 }} />}
          />

          {this.state.exist_hypermind ? <View style={ { display: 'flex', flexDirection: 'row', justifyContent: 'space-between', marginTop: 0 } }>
            <ClickableCardNew
              style={ { flex: 1 } }
              selected={this.state.bindType === 3}
              icon={this.state.customData.icon}
              title={this.state.customData.title}
              tipText={this.state.customData.tipText}
              disabled={this.state.isError}
              onPress={() => {
                if (this.state.isError) return;
                LogUtil.logOnAll('Card clicked');
                if (this.state.customData.tipText !== LocalizedStrings['car_not_set_customData'] && this.state.bindType !== 3) {
                  this.setGestureBindType(3, this.state.customData.tipText);
                } else {
                  this.goCustomAppFromUri();
                }
              }}
            />

            <ClickableCardNew
              style={ { flex: 1, marginStart: 8 } }
              selected={this.state.bindType === 2}
              icon={this.state.autoData.icon}
              title={this.state.autoData.title}
              tipText={this.state.autoData.tipText}
              disabled={this.state.isError}
              onPress={() => {
                if (this.state.isError) return;
                LogUtil.logOnAll('Card clicked');
                if (this.state.autoData.tipText !== LocalizedStrings['car_not_set_autoData'] && !CommonUtil.isValueEmpty(this.state.currentAutoRuleId) && this.state.bindType !== 2) {
                  this.setGestureBindType(2, this.state.autoData.tipText);
                } else {
                  this.goAutoAppFromUri();
                }
              }}
            />
          </View>:null}

          {Device.lastVersion >= CAR_SUPPORT_SENSITIVITY_VERSION && <SegmentWithLoadingControl
            title={ LocalizedStrings['car_sensitivity_setting'] }
            values={ [LocalizedStrings['car_sensitivity_low'] , LocalizedStrings['car_sensitivity_middle'], LocalizedStrings['car_sensitivity_high']] }
            selectedIndex={ this.state.currentSensitivityIndex }
            onChange={ (index) => {
              if (this.state.isError) return;
              if (this.state.currentSensitivityIndex === index) {
                return;
              }
              LogUtil.logOnAll(`SegmentWithLoadingControl sensitivity selected: ${ index }`);
              this._onSensitivityChanged(index);
            } }
            disabled={ this.state.isError }
          />}
        </ScrollView>
      </View>
    );
  }

  renderGridItem = ({ item, index }) => {
    const itemWidth = (CarPluginWindow.MediumLarge.Width - 148 - 16) / 3;

    return (
      <ScaleableOpacity
        onPress={() => {
          if (this.state.isError) return;
          if (this.state.selectedGridItem === index && (this.state.bindType === 0 || this.state.bindType === 1)) {
            return;
          }
          this.setState({ selectedGridItem: index, bindType: 1 }, () => {
            this.setGestureBindType(this.state.bindType, item.text);
          });
        }}
        selected={(this.state.selectedGridItem === index && (this.state.bindType === 0 || this.state.bindType === 1))}
        disabled={this.state.isError}
        style={[
          mainStyles.gridItem,
          {
            width: itemWidth,
            marginBottom: 16,
            marginLeft: index % 3 === 0 ? 0 : 8
          }
        ]}
      >
        <View style={mainStyles.gridItemContent}>
          <Image
            source={item.icon}
            style={mainStyles.gridIcon}
            resizeMode="contain"
          />
          <Text
            style={[styles.buttonTextStyle, { flex: 1 }]}
            numberOfLines={2}
          >
            {item.text}
          </Text>
        </View>
      </ScaleableOpacity>
    );
  };


  // 设置智能场景
  goAutoAppFromUri() {
    // 定义要跳转的 scheme URI
    LogUtil.logOnAll(`gestureIndex${ this.state.gestureIndex }`);
    let iid_rule_id = this.state.gesture_iid + (this.state.gestureIndex * 4 + 2);
    let iid_rule_type = this.state.gesture_iid + (this.state.gestureIndex * 4 + 4);
    let product_name = LocalizedStrings['product_name'];
    let config_name = this.state.gesture_name[this.state.gestureIndex];
    const myAppSchemeURI = `hypermind://com.mi.car.hypermind:8888/select_rule/set_info_to_prop?did=${ Device.deviceID }&pid=24442&iid_rule_id=${ iid_rule_id }&iid_rule_type=${ iid_rule_type }&rule_type=2&product_name=${ product_name }&config_name=${ config_name }`;
    LogUtil.logOnAll(`uri :${ myAppSchemeURI }`);
    try {
      native.MIOTHost.openNativePageWithUri(1, myAppSchemeURI, () => {
      });

    } catch (e) {
      LogUtil.logOnAll('goHabitAppFromUri:fail', JSON.stringify(e));
    }

  }

  // 设置自定义
  goCustomAppFromUri() {
    // 定义要跳转的 scheme URI
    let iid_rule_id = this.state.gesture_iid + (this.state.gestureIndex * 4 + 3);
    let iid_rule_type = this.state.gesture_iid + (this.state.gestureIndex * 4 + 4);
    let product_name = LocalizedStrings['product_name'];
    let config_name = this.state.gesture_name[this.state.gestureIndex];
    const myAppSchemeURI = `hypermind://com.mi.car.hypermind:8888/select_action/set_info_to_prop?did=${ Device.deviceID }&pid=24442&iid_rule_id=${ iid_rule_id }&iid_rule_type=${ iid_rule_type }&rule_type=3&product_name=${ product_name }&config_name=${ config_name }`;
    try {
      native.MIOTHost.openNativePageWithUri(1, myAppSchemeURI, () => {

      });
    } catch (e) {
      LogUtil.logOnAll('goHabitAppFromUri:fail', JSON.stringify(e));
    }

  }

  /**
   * 1:绑定的预设功能rule id
   * 2:绑定的HM 习惯 Id
   * 3:绑定的HM Action Id
   * 4:绑定的功能类型
   *
   */
  getDeviceGesTureBindData(isGetAll = false) {
    native.MIOTHost.transCustomInfo('exist_hypermind', null, (code, result) => {
      if (code) {
        this.setState({ exist_hypermind: result.data === 'true' });
        LogUtil.logOnAll(`transCustomInfo exist_hypermind: ${ JSON.stringify(result) }  ${ result.data }`);
      } else {
        this.setState({ exist_hypermind: false });
        LogUtil.logOnAll(`transCustomInfo exist_hypermind error ${ JSON.stringify(result) }`);
      }
    });
    if (this.state.car_did !== "") {
      if (isGetAll) this.getAllGestureData();
      this.getGestureBindType();
      this.getSupportPreFeatures();
    } else {
      // 1.获取车机id
      native.MIOTHost.transCustomInfo('car_did', null, (code, result) => {
        if (code) {
          LogUtil.logOnAll(`transCustomInfo: ${ JSON.stringify(result) }_${ code }`);
          this.setState({ car_did: result.data }, () => {
            if (isGetAll) this.getAllGestureData();
            this.getGestureBindType();
            this.getSupportPreFeatures();
          });
        } else {
          this.resetData(-1);
          LogUtil.logOnAll(`transCustomInfo error ${ JSON.stringify(result) }`);
        }
      });
    }
  }

  getGestureBindType() {
    // 2.获取指定手势绑定的功能类型
    let inValue = Device.deviceID;
    let currentIid = this.state.gesture_iid + (this.state.gestureIndex * 4 + 4);
    let iid = { iid_list: [currentIid] };
    let params = {
      did: this.state.car_did,
      miid: 27,
      siid: 1,
      aiid: 7,
      in: [inValue, JSON.stringify(iid)]
    };
    LogUtil.logOnAll('getGestureBindType params:', JSON.stringify(params, null, 2));
    Service.spec.doAction(params).then((res) => {
      if (!res || res.code < 0) {
        this.resetData(-1);
        LogUtil.logOnAll('getGestureBindType:doAction:fail', res);
        return;
      }
      LogUtil.logOnAll('getGestureBindType:doAction:success', JSON.stringify(res));

      try {
        // 解析返回的 JSON 字符串
        const result = JSON.parse(res[0]);
        // 使用正确的键名获取 bindType 并转换为整数
        const bindType = parseInt(result.iid_values[currentIid]) || 0; // 如果解析失败返回 0
        LogUtil.logOnAll('bindType:', bindType);

        // 根据 bindType 处理
        this.setState({
          bindType: bindType
        });
        if (bindType === 0) {
          this.resetPreSetData(0);
        } else if (bindType === -1) {
          this.resetPreSetData(-1);
        } else if (bindType === 1) {
          this.getBindRuleId(1);
        }
        this.getBindRuleId(2);
        this.getBindRuleId(3);
      } catch (error) {
        LogUtil.logOnAll('Parse response failed:', error);
        this.resetData(-1);
      }

    }).catch((e) => {
      this.resetData(-1);
      LogUtil.logOnAll('getGestureBindType:doAction:fail', JSON.stringify(e));
    });
  }

  /**
   * 0：默认值
   * 1: 预设习惯
   * 2：hm习惯
   * 3：hm action
   * @param type
   */
  getBindRuleId(type) {
    let inValue = Device.deviceID;
    let currentIid = this.state.gesture_iid + (this.state.gestureIndex * 4 + type);
    let inValue2 = { iid_list: [currentIid] };
    let params = {
      did: this.state.car_did,
      miid: 27,
      siid: 1,
      aiid: 7,
      in: [inValue, JSON.stringify(inValue2)]
    };
    LogUtil.logOnAll(`getBindRuleId params ${ type }:`, JSON.stringify(params, null, 2));
    Service.spec.doAction(params).then((res) => {
      if (!res || res.code < 0) {
        if (type === 1) {
          this.resetPreSetData(0);
        } else if (type === 2) {
          this.resetAutoData();
        } else if (type === 3) {
          this.resetCustomData();
        }
        LogUtil.logOnAll('getBindRuleId:doAction:fail', res);
        return;
      }
      LogUtil.logOnAll('getBindRuleId:doAction:success', JSON.stringify(res));

      try {
        // 解析返回的 JSON 字符串
        const result = JSON.parse(res[0]);
        if (result.hasOwnProperty("iid_values") && !CommonUtil.isPropertyValueEmpty(result.iid_values, currentIid)) {
          // 从 iid_values 中获取对应的 ruleId
          const ruleId = result.iid_values[currentIid];
          LogUtil.logOnAll(`ruleId ${ type }:`, ruleId);
          // 4.如果是习惯或者场景，需要通过iid获取名称
          if (type === 2 || type === 3) {
            if (type === 2) {
              this.setState({
                currentAutoRuleId: ruleId
              });
            }
            this.getBindNameFromRuleId(ruleId, type);
          } else if (type === 1) { // 预设习惯
            // 在 preset_ruleid 数组中查找匹配的 ruleId
            const index = this.state.preset_ruleid.indexOf(ruleId);
            if (index !== -1) {
              // 找到匹配的 ruleId，设置对应的 selectedGridItem
              this.setState({ selectedGridItem: index });
            } else {
              // 未找到匹配的 ruleId，重置为默认状态
              this.resetPreSetData(0);
            }
          } else {
            this.resetPreSetData(0);
          }
        } else {
          LogUtil.logOnAll(`ruleId ${ type } -> is null`);
          if (type === 1) {
            this.resetPreSetData(0);
          } else if (type === 2) {
            this.resetAutoData();
          } else if (type === 3) {
            this.resetCustomData();
          }
        }

      } catch (error) {
        LogUtil.logOnAll('Parse response failed:', error);
        if (type === 1) {
          this.resetPreSetData(0);
        } else if (type === 2) {
          this.resetAutoData();
        } else if (type === 3) {
          this.resetCustomData();
        }
      }
    }).catch((e) => {
      if (type === 1) {
        this.resetPreSetData(0);
      } else if (type === 2) {
        this.resetAutoData();
      } else if (type === 3) {
        this.resetCustomData();
      }
      LogUtil.logOnAll('getBindRuleId:doAction:fail', JSON.stringify(e));
    });
  }

  /**
   * 根据ruleId获取对应的名称
   * @param ruleId
   */
  getBindNameFromRuleId(ruleId, type) {
    let params = {
      did: this.state.car_did,
      miid: 27,
      siid: 1,
      aiid: 6,
      in: [ruleId]
    };
    LogUtil.logOnAll(`getBindNameFromRuleId params  ${ type }:`, JSON.stringify(params, null, 2));
    Service.spec.doAction(params).then((res) => {
      if (!res || res.code < 0) {
        if (type === 3) {
          this.resetCustomData();
        } else if (type === 2) {
          this.resetAutoData();
        }
        LogUtil.logOnAll(`getBindNameFromRuleId:doAction  ${ type }:fail code`, res);
        return;
      }
      LogUtil.logOnAll(`getBindNameFromRuleId:doAction  ${type}:success`, JSON.stringify(res));

      try {
        // 直接获取返回数组的第一个元素作为规则名称
        const ruleName = res[0];
        LogUtil.logOnAll(`${type} ruleName:`, ruleName);
        const tabs = this.state.tabs
        if (this.state.bindType === type) {
          tabs[this.state.gestureIndex].title = ruleName;
        }

        // 根据不同的 bindType 更新对应的卡片提示文本
        if (type === 2) { // 智能场景
          this.setState({
            autoData: {
              ...this.state.autoData,
              tipText: ruleName
            },
            tabs: tabs
          });
        } else if (type === 3) { // 自定义
          this.setState({
            customData: {
              ...this.state.customData,
              tipText: ruleName
            },
            tabs: tabs
          });
        }

      } catch (error) {
        if (type === 3) {
          this.resetCustomData();
        } else if (type === 2) {
          this.resetAutoData();
        }
        LogUtil.logOnAll(`Parse ruleName failed  ${type}:`, error);
      }

    }).catch((e) => {
      if (type === 3) {
        this.resetCustomData();
      } else if (type === 2) {
        // 获取不到绑定的智能场景的名称，说明改智能场景已经被删除了
        this.deleteAutoData();
      }
      LogUtil.logOnAll(`getBindNameFromRuleId:doAction  ${type}:fail catch`, JSON.stringify(e));
    });
  }

  resetData(selectPresetIndex) {
    this.setState({
      selectedGridItem: selectPresetIndex,
      bindType: -1,
      customData: {
        ...this.state.customData,
        tipText: LocalizedStrings['car_not_set_customData']
      },
      autoData: {
        ...this.state.autoData,
        tipText: LocalizedStrings['car_not_set_autoData']
      }
    });
  }

  resetPreSetData(selectPresetIndex) {
    this.setState({
      selectedGridItem: selectPresetIndex,
      bindType: 0
    });
  }

  resetCustomData() {
    this.setState({
      customData: {
        ...this.state.customData,
        tipText: LocalizedStrings['car_not_set_customData']
      }
    });
  }

  resetAutoData() {
    this.setState({
      autoData: {
        ...this.state.autoData,
        tipText: LocalizedStrings['car_not_set_autoData']
      },
      currentAutoRuleId: ""
    });
  }
  deleteAutoData() {
    this.setState({
      autoData: {
        ...this.state.autoData,
        tipText: LocalizedStrings['car_auto_delete']
      },
      currentAutoRuleId: ""
    });
  }

  setGestureBindType(bindType, gestureTitle) {
    let did = Device.deviceID;
    let iid = this.state.gesture_iid + (this.state.gestureIndex * 4 + 4);
    let params = {
      did: this.state.car_did,
      miid: 27,
      siid: 1,
      aiid: 8,
      in: [did, bindType.toString(), iid]
    };
    LogUtil.logOnAll('setPreSetBindType params:', JSON.stringify(params, null, 2));
    Service.spec.doAction(params).then((res) => {
      if (!res || res.code < 0 || res[0] === 0) {
        LogUtil.logOnAll('setPreSetBindType:doAction:fail', res);
        return;
      }
      if (res[0] === 1) {
        const tabs = this.state.tabs
        tabs[this.state.gestureIndex].title = gestureTitle
        this.setState({
          bindType: bindType,
          tabs: tabs
        });
        if (bindType === 1) {
          this.setPreSetData();
        } else if (bindType === 2) {

        } else if (bindType === 3) {

        }
        LogUtil.logOnAll('setPreSetBindType:doAction:success', JSON.stringify(res));
      }

    }).catch((e) => {
      LogUtil.logOnAll('setPreSetBindType:doAction:fail', JSON.stringify(e));
    });
  }

  setPreSetData() {
    let did = Device.deviceID;
    let ruleId = this.state.preset_ruleid[this.state.selectedGridItem];
    let iid = this.state.gesture_iid + (this.state.gestureIndex * 4 + 1);
    let params = {
      did: this.state.car_did,
      miid: 27,
      siid: 1,
      aiid: 8,
      in: [did, ruleId, iid]
    };
    LogUtil.logOnAll('setPreSetData params:', JSON.stringify(params, null, 2));
    Service.spec.doAction(params).then((res) => {
      if (!res || res.code < 0 || res[0] === 0) {
        LogUtil.logOnAll('setPreSetData:doAction:fail', res);
        return;
      }
      if (res[0] === 1) {
        LogUtil.logOnAll('setPreSetData:doAction:success', JSON.stringify(res));

      }

    }).catch((e) => {
      LogUtil.logOnAll('setPreSetData:doAction:fail', JSON.stringify(e));
    });
  }

  /**
   * 获取开关状态
   */
  getGestureSwitch() {
    let params = [{
      did: Device.deviceID,
      siid: 2,
      piid: 1
    }];
    Service.spec.getPropertiesValue(params).then((res) => {
      LogUtil.logOnAll("getPropertiesValue res: ", res);
      if (!res || res.code < 0 || res[0] === 0) {
        return;
      }
      this.setState({ gestureSwitch: res[0].value });
    }).catch((e) => {
      LogUtil.logOnAll('getPropertiesValue error:', JSON.stringify(e));
    });
  }

  /**
   * 获取灵敏度
   */
  getSensitivitySetting() {
    Service.spec.getPropertiesValue([{
      did: Device.deviceID,
      siid: 3,
      piid: 2
    }]).then((res) => {
      LogUtil.logOnAll("=============get Sensitivity status success", res);
      if (!res || res.code < 0 || res[0] === 0) {
        return;
      }
      this.setState({ currentSensitivityIndex: res[0].value });
    }).catch((e) => {
      LogUtil.logOnAll('=============get Sensitivity status error:', JSON.stringify(e));
    });
  }

  /**
   * 设置开关状态
   */
  setGestureSwitch(value) {
    let params = [{
      did: Device.deviceID,
      siid: 2,
      piid: 1,
      value: value
    }];
    Service.spec.setPropertiesValue(params).then((res) => {
      LogUtil.logOnAll("setPropertiesValue res : ", res);
      this.setState({ gestureSwitch: value });
    }).catch((e) => {
      LogUtil.logOnAll('setPropertiesValue error:', JSON.stringify(e));
    })
  }

  /**
   * 设置灵敏度
   */
  _onSensitivityChanged(value) {
    let params = [{
      did: Device.deviceID,
      siid: 3,
      piid: 2,
      value: value
    }];
    Service.spec.setPropertiesValue(params).then((res) => {
      this.setState({ currentSensitivityIndex: value });
      LogUtil.logOnAll("Sensitivity setPropertiesValue res : ", res);
    }).catch((e) => {
      Toast.fail('c_set_fail');
      LogUtil.logOnAll('Sensitivity setPropertiesValue error:', JSON.stringify(e));
    });
  }

  /**
   * 获取所有手势的绑定功能
   */
  getAllGestureData() {
    let iid = { iid_list: ['99.1.4', '99.1.8', '99.1.12', '99.1.16', '99.1.20', '99.1.24'] };
    let params = {
      did: this.state.car_did,
      miid: 27,
      siid: 1,
      aiid: 7,
      in: [Device.deviceID, JSON.stringify(iid)]
    };
    Service.spec.doAction(params).then((res) => {
      if (!res || res.code < 0) {
        this.resetData(-1);
        return;
      }
      const tabs = this.state.tabs;
      const iid_list = []
      try {
        const iidValues = JSON.parse(res[0]).iid_values;
        iid.iid_list.forEach((iid, index) => {
          const bindType = parseInt(iidValues[iid]) || 0; // 如果解析失败返回 0
          iid_list.push({tabIndex: index, type: bindType, iid: this.state.gesture_iid + (index * 4 + bindType)});
        })
        this.getAllRuleData(tabs, iid_list)
      } catch (error) {
        LogUtil.logOnAll('Parse response failed:', error);
        this.resetData(-1);
      }

    }).catch((e) => {
      this.resetData(-1);
      LogUtil.logOnAll('getAllGestureData:doAction:fail', JSON.stringify(e));
    });
  }

  findLastTypeIndex(data) {
    for (let i = data.length - 1; i >= 0; i--) {
      if (data[i].type === 2 || data[i].type === 3) {
        return i;
      }
    }
    return -1; // 没找到返回 -1
  }

  /**
   * 获取所有自定义或智能场景rule
   */
  getAllRuleData(tabs, iid_list) {
    try {
      let params = {
        did: this.state.car_did,
        miid: 27,
        siid: 1,
        aiid: 7,
        in: [Device.deviceID, JSON.stringify({ iid_list: iid_list.map((item) => item.iid) }),]
      };
      LogUtil.logOnAll(`getAllRuleData params:`, JSON.stringify(params, null, 2));
      Service.spec.doAction(params).then((res) => {
        if (!res || res.code < 0) {
          LogUtil.logOnAll('getAllRuleData:doAction:fail', res);
          return;
        }
        LogUtil.logOnAll('getAllRuleData:doAction:success', JSON.stringify(res));
        const iidValues = JSON.parse(res[0]).iid_values;
        let lastCustomIndex = this.findLastTypeIndex(iid_list);
        iid_list.forEach((item, index) => {
          const ruleId = iidValues[item.iid]
          if (!ruleId) {
            tabs[index].title = LocalizedStrings['car_grid_no_function'];
            if (index === iid_list.length - 1 && lastCustomIndex === -1) {
              this.setState({ tabs: tabs });
            }
            return;
          }
          let presetRuleIndex = this.state.preset_ruleid.indexOf(ruleId);
          if (item.type === 1 && ruleId === 1) {
            presetRuleIndex = 0;
          }
          if (presetRuleIndex !== -1) {
            tabs[index].title = this.state.gridItems[presetRuleIndex].text;
            if (index === iid_list.length - 1 && lastCustomIndex === -1) {
              this.setState({ tabs: tabs });
            }
          } else if (item.type <= 1) {
            tabs[index].title = LocalizedStrings['car_grid_no_function'];
            if (index === iid_list.length - 1 && lastCustomIndex === -1) {
              this.setState({ tabs: tabs });
            }
          } else {
            this.getBindNameWithRuleId(ruleId)
              .then((res) => {
                tabs[index].title = res;
              })
              .catch((e) => {
                if (item.type > 1) {
                  tabs[index].title = LocalizedStrings['car_auto_delete'];
                }
                LogUtil.logOnAll('getAllRuleData:doAction:fail index: ' + index, JSON.stringify(e));
              })
              .finally(() => {
                if (index === iid_list.length - 1) {
                  this.setState({ tabs: tabs });
                } else if (index === lastCustomIndex) {
                  LogUtil.logOnAll('tabs: ', tabs);
                  this.setState({tabs: tabs});
                }
              })
          }
        });
      });
    } catch (error) {
      LogUtil.logOnAll('Parse response failed:', error);
      this.resetData(-1);
    }
  }

  /**
   * 根据ruleId获取对应的名称
   * @param ruleId
   */
  async getBindNameWithRuleId(ruleId) {
    let params = {
      did: this.state.car_did,
      miid: 27,
      siid: 1,
      aiid: 6,
      in: [ruleId]
    };
    LogUtil.logOnAll(`getBindNameFromRuleId params:`, JSON.stringify(params, null, 2));
    return new Promise((resolve, reject) => {
      Service.spec.doAction(params).then((res) => {
        if (!res || res.code < 0) {
          LogUtil.logOnAll(`getBindNameFromRuleId:doAction :fail code`, res);
          resolve('');
        }
        LogUtil.logOnAll(`getBindNameFromRuleId:doAction :success`, JSON.stringify(res));
        try {
          // 直接获取返回数组的第一个元素作为规则名称
          const ruleName = res[0];
          resolve(ruleName);
        } catch (error) {
          LogUtil.logOnAll(`Parse ruleName failed:`, error);
          resolve('');
        }

      }).catch((e) => {
        LogUtil.logOnAll(`getBindNameFromRuleId:doAction:fail catch`, JSON.stringify(e));
        reject(e);
      });
    })
  }

  getSupportPreFeatures() {
    let ruleIds = ['1005'];
    let params = {
      did: this.state.car_did,
      miid: 27,
      siid: 1,
      aiid: 9,
      in: [JSON.stringify(ruleIds)]
    };
    LogUtil.logOnAll(`getSupportPreFeatures params:`, JSON.stringify(params, null, 2));
    Service.spec.doAction(params).then((res) => {
      LogUtil.logOnAll(`getSupportPreFeatures:doAction :`, res);
      if (!res[0] || res.code < 0) {
        let newGridItems = this.state.gridItems;
        newGridItems[5].visible = false;
        this.setState({
          gridItems: newGridItems
        });
        return;
      }
      const result = JSON.parse(res[0]);
      LogUtil.logOnAll(`getSupportPreFeatures: result :`, result[0].state);
      let newGridItems = this.state.gridItems;
      newGridItems[5].visible = result[0].state;
      this.setState({
        gridItems: newGridItems
      });

    }).catch((e) => {
      let newGridItems = this.state.gridItems;
      newGridItems[5].visible = false;
      this.setState({
        gridItems: newGridItems
      });
      LogUtil.logOnAll('getSupportPreFeatures:doAction:fail', JSON.stringify(e));
    });
  }

  getDeviceOnlineData() {
    this.getDeviceGesTureBindData(true);
    this.getThermalStatus();
  }

  setAppListener() {
    // 添加页面显示监听
    this.viewDidAppearListener = PackageEvent.packageDidResume.addListener(() => {
      LogUtil.logOnAll('MainCarPage packageDidResume');
      // 在这里处理页面显示时的逻辑
      this.getDeviceGesTureBindData(true);
    });
    // 监听设备离线在线
    this._deviceOnlineListener = DeviceEvent.deviceStatusChanged.addListener((device, newstatus) => {
      LogUtil.logOnAll(TAG, `设备状态改变: ${ JSON.stringify(newstatus) } ,${ newstatus.isOnline }`);
      if (newstatus.isOnline) {
        this.getDeviceOnlineData();
      }
    });

    Device.getDeviceWifi().subscribeMessages('prop.3.1', 'prop.2.1').then((subcription) => {
      this.mSubcription = subcription;
    }).catch((error) => {
      LogUtil.logOnAll('subscribeMessages error', error);
    });

    // 监听设备属性发生变化事件； 当设备属性发生改变，会发送事件到js，此处会收到监听回调
    this.mDeviceReceivedMessages = DeviceEvent.deviceReceivedMessages.addListener(
      (device, map, res) => {
        LogUtil.logOnAll('Device.addListener', res);
        let key = res[0].key;
        let value = res[0].value[0];
        if (key === "prop.3.1") {
          this.setState({
            isError: value === 1
          });
        } else if (key === "prop.2.1") {
          this.setState({ gestureSwitch: value });
        }
      });
  }
}