import React from 'react';
import PropTypes from 'prop-types';
import { View, Animated, ViewPropTypes, Easing, TouchableOpacity, Text, Image } from 'react-native';
import { styles, Radius, Constants, Opacity } from 'micariot-ui-sdk/common/styles/Styles';

const DEFAULT_SEGMENT_HEIGHT = 88;
const DEFAULT_MARGIN = 24;

/**
 * @export public
 * @module SegmentWithLoadingControl
 * @description SegmentWithLoadingControl for CarIoT
 * @property {object} style - style
 * @property {string} title - 标题，如果未空则不显示标题区域
 * @property {array} values - 分段文本
 * @property {array} tipsArray - 分段提示数组
 * @property {number} selectedIndex - 选择index
 * @property {function} onChange - 选择回调方法
 * @property {bool} disabled - 是否禁用，默认false
 */
class SegmentWithLoadingControl extends React.PureComponent {
  static propTypes = {
    style: PropTypes.object,
    title: PropTypes.string,
    values: PropTypes.array,
    tipsArray: PropTypes.array,
    selectedIndex: PropTypes.number,
    onChange: PropTypes.func,
    disabled: PropTypes.bool
  };

  constructor(props) {
    super(props);

    this.offsetHeight = 0;
    this.loadingTimer = null; // 加载定时器

    this.state = {
      selectedIndex: this.props.selectedIndex,
      segmentDimension: { width: 0, height: 0 },
      activeSegmentPosition: { x: this.offsetHeight, y: this.offsetHeight },
      positionAnimationValue: new Animated.Value(0),
      isLoading: false // 加载状态
    };
  }

  componentDidUpdate(pervProps) {
    if (this.props.selectedIndex !== pervProps.selectedIndex) {
      this.onSegmentSelection(this.props.selectedIndex);
    }
  }

  /**
   * 组件卸载时清理定时器
   */
  componentWillUnmount() {
    if (this.loadingTimer) {
      clearTimeout(this.loadingTimer);
      this.loadingTimer = null;
    }
  }

  /**
   * On segment change event.
   *
   * @param {Number} index
   */
  onSegmentSelection = (index) => {
    // 如果正在加载中，忽略点击事件
    if (this.state.isLoading) {
      return;
    }

    // 清除之前的定时器
    if (this.loadingTimer) {
      clearTimeout(this.loadingTimer);
      this.loadingTimer = null;
    }

    // 开始加载状态
    this.setState({ isLoading: true });

    const animate = () => {
      Animated.timing(this.state.positionAnimationValue, {
        toValue: this.state.activeSegmentPosition.x,
        duration: 150,
        easing: Easing.ease
      }).start(() => { });
    };

    this.setState(
      (prevState) => ({
        selectedIndex: index,
        activeSegmentPosition: { x: prevState.segmentDimension.width * index + this.offsetHeight, y: prevState.activeSegmentPosition.y }
      }),
      animate
    );

    // 启动3秒定时器
    this.loadingTimer = setTimeout(() => {
      this.setState({ isLoading: false });
      this.loadingTimer = null;
      // 调用onChange回调
      this.props.onChange && this.props.onChange(index);
    }, 3000);
  }

  /**
   * @param {Object} event
   */
  segmentOnLayout = (event) => {
    const { width, height } = event.nativeEvent.layout;
    const segmentWidth = (width - this.offsetHeight * 2) / this.props.values.length;

    const animate = () => {
      Animated.timing(this.state.positionAnimationValue, {
        toValue: segmentWidth * this.state.selectedIndex + this.offsetHeight,
        duration: 0
      }).start();
    };

    this.setState(() => ({
      segmentDimension: { width: segmentWidth, height }
    }), animate);
  }

  render() {
    const { width, height } = this.state.segmentDimension;
    const segmentHeight = height - this.offsetHeight * 2;
    // 当loading或disabled时，组件都应该被禁用
    const isDisabled = this.state.isLoading || this.props.disabled;
    const opacity = isDisabled ? Opacity.Disabled : Opacity.Normal;

    return (
      <View style={[{
        marginTop: Constants.DEFAULT_MARGIN_TOP_BOTTOM,
        opacity
      }, this.props.style]}
      pointerEvents={isDisabled ? "none" : "auto"}
      >
        {this.props.title ? <Text style={[styles.titleTextStyle, { marginBottom: DEFAULT_MARGIN }]}>{this.props.title}</Text> : null}
        <View
          style={[styles.borderStyle, { flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center', height: DEFAULT_SEGMENT_HEIGHT }]}
          onLayout={this.segmentOnLayout}
        >
          {this.props.values &&
            this.props.values.map((segment, index) => {
              return (
                // eslint-disable-next-line react/jsx-key
                <Segment
                  style={{ flex: 1, height: segmentHeight }}
                  title={segment}
                  textStyle={index !== this.state.selectedIndex ? styles.buttonTextStyle : styles.buttonHightLightTextStyle}
                  onPress={() => this.onSegmentSelection(index)}
                  isLoading={this.state.isLoading && index === this.state.selectedIndex}
                />
              );
            })}
          <Animated.View
            style={[
              {
                flex: 1,
                zIndex: 5,
                position: 'absolute',
                width,
                height: segmentHeight,
                left: this.state.positionAnimationValue,
                top: this.state.activeSegmentPosition.y,
                borderRadius: Radius.WidgetLevel
              },
              styles.itemHighlightStyle
            ]}
          />
        </View>
        {this.props.tipsArray ? <Text numberOfLines={1} style={[{ marginTop: DEFAULT_MARGIN }, styles.subTitleTextStyle]}>{this.props.tipsArray[this.state.selectedIndex]}</Text> : null }
      </View>
    );
  }
}

const Segment = ({ title, style, textStyle, onPress, isLoading }) => {
  // 创建旋转动画值
  const rotateAnim = React.useRef(new Animated.Value(0)).current;

  // 启动旋转动画
  React.useEffect(() => {
    if (isLoading) {
      const startRotation = () => {
        rotateAnim.setValue(0);
        Animated.timing(rotateAnim, {
          toValue: 1,
          duration: 1000,
          easing: Easing.linear,
          useNativeDriver: true,
        }).start(() => {
          if (isLoading) {
            startRotation();
          }
        });
      };
      startRotation();
    } else {
      rotateAnim.stopAnimation();
    }
  }, [isLoading, rotateAnim]);

  const rotate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <TouchableOpacity style={[styles.buttonBaseStyle, { zIndex: 10, flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }, style]} onPress={onPress}>
      {isLoading && (
        <Animated.Image
          source={require('../../Resources/Main/ic_loader.png')}
          style={{
            width: 40,
            height: 40,
            marginRight: 8,
            transform: [{ rotate }]
          }}
        />
      )}
      <Text style={[styles.buttonTextStyle, textStyle]}>{title}</Text>
    </TouchableOpacity>
  );
};

Segment.propTypes = {
  title: PropTypes.string.isRequired,
  textStyle: ViewPropTypes.style.isRequired,
  onPress: PropTypes.func.isRequired,
  style: ViewPropTypes.style,
  isLoading: PropTypes.bool
};

export default SegmentWithLoadingControl;
export { SegmentWithLoadingControl };