// MainCarPage中修改后的_onSensitivityChanged方法示例

import React, { Component } from 'react';
import { View } from 'react-native';
import { Toast } from 'miot/ui';
import { Device, Service } from 'miot';
import SegmentWithLoadingControl, { LOADING_DURATION } from './car/widget/SegmentWithLoadingControl';
import LogUtil from './utils/LogUtil'; // 假设的LogUtil路径

class MainCarPage extends Component {
  constructor(props) {
    super(props);
    this.state = {
      currentSensitivityIndex: 0
    };
    this.errorTimers = new Set(); // 用于管理多个错误定时器
  }

  componentWillUnmount() {
    // 清理所有错误定时器
    this.errorTimers.forEach(timer => {
      clearTimeout(timer);
    });
    this.errorTimers.clear();
  }

  /**
   * 延迟显示错误Toast的辅助方法
   */
  _showDelayedError = (message) => {
    const timer = setTimeout(() => {
      Toast.fail(message);
      this.errorTimers.delete(timer);
    }, LOADING_DURATION);
    
    this.errorTimers.add(timer);
  };

  /**
   * 灵敏度改变的方法（修改后）
   */
  _onSensitivityChanged(value) {
    let params = [{
      did: Device.deviceID,
      siid: 3,
      piid: 2,
      value: value
    }];
    
    Service.spec.setPropertiesValue(params).then((res) => {
      if (res[0].code >= 0) {
        this.setState({ currentSensitivityIndex: value });
      } else {
        // 延迟显示错误Toast，与loading时间同步
        this._showDelayedError('c_set_fail');
      }
      LogUtil.logOnAll("Sensitivity setPropertiesValue res : ", JSON.stringify(res));
    }).catch((e) => {
      // 延迟显示错误Toast，与loading时间同步
      this._showDelayedError('c_set_fail');
      LogUtil.logOnAll('Sensitivity setPropertiesValue error:', JSON.stringify(e));
    });
  }

  render() {
    return (
      <View style={{ flex: 1, padding: 20 }}>
        <SegmentWithLoadingControl
          title="灵敏度设置"
          values={['低', '中', '高']}
          tipsArray={['低灵敏度', '中等灵敏度', '高灵敏度']}
          selectedIndex={this.state.currentSensitivityIndex}
          onChange={this._onSensitivityChanged.bind(this)}
          disabled={false}
        />
      </View>
    );
  }
}

export default MainCarPage;

/**
 * 修改要点：
 * 
 * 1. 导入LOADING_DURATION常量：
 *    import SegmentWithLoadingControl, { LOADING_DURATION } from './car/widget/SegmentWithLoadingControl';
 * 
 * 2. 创建_showDelayedError辅助方法：
 *    - 使用LOADING_DURATION作为延迟时间
 *    - 管理定时器，避免内存泄漏
 * 
 * 3. 修改Toast.fail调用：
 *    - 将 Toast.fail('c_set_fail') 改为 this._showDelayedError('c_set_fail')
 *    - 自动使用与loading相同的延迟时间
 * 
 * 4. 优点：
 *    - 只需要在一个地方修改LOADING_DURATION
 *    - 错误提示与loading结束时间完全同步
 *    - 代码更加一致和可维护
 */
